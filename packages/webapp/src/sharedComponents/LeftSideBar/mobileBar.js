/* eslint-disable @next/next/no-img-element */
import React from 'react';
import { connect } from 'react-redux';
import { withTranslation } from 'react-i18next';
import Router from 'next/router';
import PropTypes from 'prop-types';
import { get } from 'lodash';
import * as moment from 'moment';
import {
  setEditSection,
  setTheme,
  toggleTheme,
  updateProjectData,
  setIsClickedOnMenu,
  updateProjectMeta,
} from 'reducer/project';
import { logout } from 'reducer/auth';
import Modal from './notificationModal';
import Style from '../styles/leftSideBar.module.scss';
import Styles from '../styles/notification.module.scss';
import InlineSvg from 'sharedComponents/inline-svg';

// Project Creation form component
class LeftSideBar extends React.PureComponent {
  constructor(props) {
    super(props);
    this.state = {
      notificationStatus: false,
    };
  }

  linkButtonHandler = () => {
    const { projectPreviewData } = this.props;
    Router.push(
      '/project/share/[shareHistory]',
      `/project/share/${projectPreviewData._id}`,
    );
  };

  // Show hide notification modal
  toggleNotification = () => {
    const { notificationStatus } = this.state;
    const { updateProjectMeta, projectPreviewData } = this.props;
    updateProjectMeta({ isNewNotification: false }, projectPreviewData._id);
    this.setState({ notificationStatus: !notificationStatus });
  };

  // Calculate day and hours.
  diffDaysHours = (date) => {
    const showTime = {};
    const currentDate = moment().format('YYYY-MM-DD');
    const createdDate = moment(date).format('YYYY-MM-DD');

    const diffDate = moment(`${currentDate}`, 'YYYY-MM-DD').diff(
      moment(`${createdDate}`, 'YYYY-MM-DD'),
      'days',
    );

    if (diffDate > 1) {
      showTime.days = `${diffDate}  days ago`;
    } else {
      showTime.days = `${diffDate}  day ago`;
    }

    const currentHours = moment().format('hh');
    const createdHours = moment(date).format('hh');

    const diffHours = moment(`${currentHours}`, 'hh').diff(
      moment(`${createdHours}`, 'hh'),
      'hours',
    );

    showTime.hours = `${diffHours} hours ago`;
    showTime.numberOfDays = diffDate;

    return showTime;
  };

  // This method is used for notification modal body.
  body = () => {
    const { notificationsList } = this.props;
    return (
      <div className="mt-5 mt-sm-5 mt-md-2">
        {notificationsList === null ? (
          <div className="text-left">
            <p className="text-primary p2 pt-3">
              This is where you’ll find feedback to your project when it’s
              shared with decision makers.
            </p>
          </div>
        ) : (
          notificationsList &&
          notificationsList.map((item, index) => {
            // Defensive check for activities array
            const activity = get(item, 'activities[0]', {});
            const user = get(activity, 'user', {});
            const action = get(activity, 'action', '');

            return (
              <div key={index}>
                <div className="col-12 d-flex flex-row p-0 text-left">
                  <div className="col-3 rounded-circle pl-0">
                    {get(user, 'profileImage') !== '' &&
                    get(user, 'profileImage') ? (
                      <img
                        src={get(user, 'profileImage')}
                        height="50px"
                        width="50px"
                        className="rounded-circle"
                        alt=""
                        style={{ cursor: 'pointer' }}
                      />
                    ) : (
                      <img
                        src="/assets/jpg/Placeholder_Avatar_320.jpg"
                        height="50px"
                        width="50px"
                        className="rounded-circle"
                        alt=""
                        style={{ cursor: 'pointer' }}
                      />
                    )}
                  </div>
                  <div className="col-9 p-0">
                    {action === 'letsTalk' && (
                      <p className="p2" style={{ color: '#05012D' }}>
                        {get(user, 'name')} wants to talk about this project.
                        You can contact them via email.{' '}
                        <a
                          href={`mailto: ${get(user, 'email')}`}
                          className=""
                          style={{ color: '#1743d7' }}
                        >
                          email
                        </a>
                        .
                      </p>
                    )}
                    {action === 'tracking' && (
                      <p className="p2" style={{ color: '#05012D' }}>
                        {get(user, 'name')} is tracking this project .
                      </p>
                    )}
                    {action === 'notInterested' && (
                      <p className="p2" style={{ color: '#05012D' }}>
                        {get(user, 'name')} is not interested in this project at
                        the moment.
                      </p>
                    )}
                  </div>
                </div>
                <div className="col-12 d-flex flex-row p-0 text-left mb-3">
                  <div className="col-3 pl-0" />
                  <div className="col-9 p-0 d-flex flex-row">
                    <div
                      style={
                        action === 'letsTalk'
                          ? {
                              backgroundColor: '#00E5D5',
                              width: '12px',
                              height: '12px',
                            }
                          : action === 'tracking'
                            ? {
                                backgroundColor: '#EBFF29',
                                width: '12px',
                                height: '12px',
                              }
                            : {
                                backgroundColor: '#FF303D',
                                width: '12px',
                                height: '12px',
                              }
                      }
                      className="rounded-circle p-0"
                    />
                    <div className="col-11">
                      <p className="p3" style={{ color: '#858585' }}>
                        {this.diffDaysHours(item.updatedAt).numberOfDays !== 0
                          ? this.diffDaysHours(item.updatedAt).days
                          : this.diffDaysHours(item.updatedAt).hours}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            );
          })
        )}
      </div>
    );
  };

  render() {
    const { projectPreviewData, isNewNotification } = this.props;
    const { notificationStatus } = this.state;

    return (
      <div>
        <div className="row">
          <div className="col-8">
            {get(projectPreviewData, 'cover.title') && (
              <h2 className={`${Style.projectsTitle}`} data-cy="headerId">
                {projectPreviewData.cover.title}
              </h2>
            )}
          </div>
          <div className="col-2">
            <div className="position-relative">
              <InlineSvg
                src="/assets/svg/Bell.svg"
                height="28px"
                width="28px"
                className=""
                alt=""
                style={{ cursor: 'pointer' }}
                onClick={() => this.toggleNotification()}
              />
              {isNewNotification && (
                <div
                  className={`${Styles.notificationCount} carousel-caption text-light rounded-circle p4`}
                  onClick={() => this.toggleNotification()}
                >
                  1
                </div>
              )}
            </div>
            <Modal
              modalShow={notificationStatus}
              title="Feedback"
              body={this.body()}
              closeCallback={this.toggleNotification}
              isShowCrossBtn
            />
          </div>
          <div className="col-2">
            <div className="position-relative">
              <InlineSvg
                src="/assets/svg/shareIcon.svg"
                height="28px"
                width="28px"
                className=""
                alt=""
                style={{ cursor: 'pointer' }}
                onClick={() => this.linkButtonHandler()}
              />
            </div>
          </div>
        </div>
        <p className={`${Style.registrationText} p2 mt-1`} data-cy="regNo">
          SMASH REGISTRATION #
          {get(projectPreviewData, 'regNo') && projectPreviewData.regNo}
        </p>
      </div>
    );
  }
}

LeftSideBar.propTypes = {
  projectPreviewData: PropTypes.object.isRequired,
  cover: PropTypes.object.isRequired,
  notificationsList: PropTypes.array.isRequired,
  updateProjectMeta: PropTypes.func.isRequired,
  isNewNotification: PropTypes.number.isRequired,
};

const mapStateToProps = (state) => ({
  editSection: state.project.editSection,
  userData: state.auth.userData,
  isClickedOnMenu: state.project.isClickedOnMenu,
  isNewNotification: state.project.isNewNotification,
  notificationsList: state.project.notificationsList,
});

const mapDispatchToProps = (dispatch) => {
  return {
    setEditSection: (payload) => dispatch(setEditSection(payload)),
    toggleTheme: (payload) => dispatch(toggleTheme(payload)),
    updateProjectData: (value, id) => dispatch(updateProjectData(value, id)),
    setTheme: (payload) => dispatch(setTheme(payload)),
    logout: (payload) => dispatch(logout(payload)),
    setIsClickedOnMenu: (payload) => dispatch(setIsClickedOnMenu(payload)),
    updateProjectMeta: (payload, id) =>
      dispatch(updateProjectMeta(payload, id)),
  };
};

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(withTranslation('common')(LeftSideBar));
