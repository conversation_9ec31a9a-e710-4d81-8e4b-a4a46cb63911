const { errorHandler } = require('../utils/errorHandler');
const ProjectSnapServices = require('../services/ProjectSnapServices');
const { get, isEmpty } = require('lodash');
const Socket = require('../../config/socket-io');
const projectSnapServices = new ProjectSnapServices();

/**
 * @class Project Snap Controller
 */

class ProjectSnapController {
  /**
   *
   * @param {*} request to get payload and params
   * @param {*} h return type
   * @returns Project snap shot details
   */
  static async updateSnapActivities(request, h) {
    try {
      request.logger.info(
        'ProjectSnapController.updateSnapActivities method called',
      );
      const payload = request.payload;

      const where = {
        _id: payload.projectSnapId, // Match the specific ProjectSnaps document
        'activities.user.email': payload.user.email, // Match the specific activity by its _id
      };
      const query = {
        $set: {
          'activities.$.action': payload.action,
        },
      };
      const findUserSnap = await projectSnapServices.get(where);
      if (!isEmpty(findUserSnap) && payload.action === 'view') {
        return h
          .response({
            statusCode: 200,
            message: 'Project snap update successfully',
            data: findUserSnap,
          })
          .code(200);
      }
      request.logger.info(
        'ProjectSnapController.updateSnapActivities set data is exist',
      );
      let result = await projectSnapServices.update(where, query);
      if (result.modifiedCount === 0) {
        request.logger.info(
          'ProjectSnapController.updateSnapActivities push object if not exist',
        );
        const updatePipeline = {
          $push: {
            activities: { action: payload.action, user: payload.user }, // Push a new activity
          },
        };
        result = await projectSnapServices.update(
          { _id: payload.projectSnapId },
          updatePipeline,
        );
      }
      // Get the updated snap result without user email filter to get complete data
      const findUserSnapResult = await projectSnapServices.get({
        _id: payload.projectSnapId,
      });

      // Emit socket notification to project creator when someone takes action
      if (process.env.NODE_ENV !== 'testing' && payload.action !== 'view') {
        const io = Socket.get();
        if (io && findUserSnapResult) {
          const notificationData = {
            isNewNotification: true,
            result: findUserSnapResult,
          };

          // Emit to the project creator's room (using creator's userId as room name)
          const creatorUserId = get(findUserSnapResult, 'creator.userId');
          if (creatorUserId) {
            io.to(creatorUserId.toString()).emit('feedback-notification', {
              data: notificationData,
            });
            request.logger.info(
              `Socket notification sent to creator ${creatorUserId} for action ${payload.action}`,
            );
          }
        }
      }

      return h
        .response({
          statusCode: 200,
          message: 'Project snap update successfully',
          data: findUserSnapResult,
        })
        .code(200);
    } catch (err) {
      request.logger.error(
        `ProjectSnapController.updateSnapActivities method error ${err}`,
      );
      return errorHandler(err);
    }
  }

  /**
   *
   * @param {*} request to get payload and params
   * @param {*} h return type
   * @returns Project snap shot details
   */
  static async getProjectSnap(request, h) {
    try {
      request.logger.info('ProjectSnapController.getProjectSnap method called');
      const email = get(request, 'user.email');
      request.logger.info(
        'ProjectSnapController.getProjectSnap set data is exist',
      );
      const where = {
        'activities.user.email': email,
      };
      const result = await projectSnapServices.find(where);
      return h
        .response({
          statusCode: 200,
          message: 'Project snap get successfully',
          data: result,
        })
        .code(200);
    } catch (err) {
      request.logger.error(
        `ProjectSnapController.getProjectSnap method error ${err}`,
      );
      return errorHandler(err);
    }
  }
}

module.exports = ProjectSnapController;
