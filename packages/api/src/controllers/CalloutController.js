const {
  getCreatorFormateObject,
  getDiscovererFormateObject,
  getSlateStatusLabel,
} = require('../utils/helpers');
const { get, last, find, uniq, isEmpty, toUpper, floor } = require('lodash');
const CalloutService = require('../services/CalloutService');
const ProjectSnaps = require('../models/projectSnaps');
const Callout = require('../models/callout');

const { errorHandler } = require('../utils/errorHandler');
const SubscriptionServices = require('../services/Subscription');
const UserService = require('../services/UserService');
const PlanController = require('./PlanController');
const MailService = require('../services/MailService');
const AuthService = require('../services/AuthService');
const userSerVice = new UserService();
const subscriptionServices = new SubscriptionServices();
class CalloutController {
  /**
   * Get Callout by query params
   * Parse query from query params
   * @param {Hapi request obj} request
   * @param {hapi handler} h
   */
  static async get(request, h) {
    try {
      const query = request.parsedQuery;
      const { where, options } = query;
      const result = await CalloutService.pagination(where, options);
      return h
        .response({
          statusCode: 200,
          message: 'Get Callout successfully',
          data: result,
        })
        .code(200);
    } catch (err) {
      request.logger.error('error in CalloutController.get', err);
      errorHandler(err);
    }
  }

  /**
   * This method is responsible to create callout
   * Get in array and insert in tag
   * @param {Hapi request obj} request
   * @param {hapi handler} h
   */
  static async create(request, h) {
    try {
      const user = request.user;
      let discovererPayload = {};

      const { discovererId, ...restOfPayload } = request.payload;

      if (discovererId) {
        // Attempt to fetch the discoverer details
        const discovererData = await userSerVice.getUserList(
          { _id: discovererId },
          user.email,
        );
        const discoverer = get(discovererData, 'docs[0]', null);

        // If discoverer data exists, format it and add to payload
        if (discoverer) {
          discovererPayload = {
            discoverer: getDiscovererFormateObject(discoverer),
          };
        }
      }

      /* Create callout */
      const result = await CalloutService.create({
        ...restOfPayload,
        creator: getCreatorFormateObject(user),
        ...discovererPayload,
      });

      return h
        .response({
          statusCode: 200,
          message: 'Callout created successfully',
          data: result,
        })
        .code(200);
    } catch (err) {
      request.logger.error('error in CalloutController.create', err);
      errorHandler(err);
    }
  }

  /**
   * This method is responsible for increased tag used count
   * @param {Hapi request obj} request
   * @param {hapi handler} h
   */
  static async update(request, h) {
    try {
      const { id } = request.params;
      const { discovererId, ...restOfPayload } = request.payload;

      // Fetch discoverer details if discovererId is provided
      let discoverer = null;
      if (discovererId) {
        const user = request.user;
        const discovererData = await userSerVice.getUserList(
          { _id: discovererId },
          user.email,
        );
        discoverer = get(discovererData, 'docs[0]', {});
      }
      // Prepare the update payload
      const updatePayload = {
        ...restOfPayload,
        ...(discoverer
          ? { discoverer: getDiscovererFormateObject(discoverer) }
          : {}),
      };

      const callout = await Callout.findOne({
        _id: id,
      });
      // Perform the update
      const result = await CalloutService.patch({ _id: id }, updatePayload);
      if (
        result &&
        callout.isPublished === false &&
        restOfPayload.isPublished
      ) {
        const mailData = {
          email: get(result, 'discoverer.email', ''),
          to: get(result, 'discoverer.email', ''),
          id: get(result, 'discoverer.id', ''),
          subject: `Your SMASH Call Out is Live`,
          agenda_type: `Your SMASH Call Out is Live`,
          message_data: {
            link: `${process.env.WEBAPP_BASE_URL}/callouts/view/${id}`,
            email: get(result, 'discoverer.email', ''),
            calloutLogo: get(result, 'body.logo', ''),
            companyName: get(result, 'body.companyName', ''),
            name: get(result, 'name', '').toUpperCase(),
          },
        };

        await MailService.calloutPublishEmail(mailData);
      }
      return h
        .response({
          statusCode: 200,
          message: 'Callout updated successfully',
          data: result,
        })
        .code(200);
    } catch (err) {
      request.logger.error('error in CalloutController.update', err);
      errorHandler(err);
    }
  }

  /**
   * This method is responsible to add submission to the callout
   * @param {Hapi request obj} request
   * @param {hapi handler} h
   */
  static async addSubmission(request, h) {
    try {
      const { id } = request.params;
      const user = request.user;
      const payload = request.payload;
      // Perform the update
      const projectSnap = await ProjectSnaps.findOne({
        _id: payload.id,
      });

      if (!projectSnap) {
        request.logger.error(`${payload.id} snapshot does not exist!`);
        return h
          .response({
            statusCode: 404,
            message: `snapshot does not exist!`,
            error: `not_found`,
          })
          .code(404);
      }
      const calloutData = await CalloutService.findOneCallout({
        _id: id,
        'submissions.snapshot.id': payload.id,
      });
      if (calloutData) {
        return h
          .response({
            statusCode: 409,
            message: 'Callout submission already exist',
            data: calloutData,
          })
          .code(409);
      }

      // const calloutExist =
      const result = await CalloutService.patch(
        { _id: id },
        {
          $push: {
            submissions: {
              creator: getCreatorFormateObject(user),
              'snapshot.id': payload.id,
              'snapshot.body': projectSnap.body,
              'snapshot.title': projectSnap.notes,
              'snapshot.hash': projectSnap.hash,
              feedback: [],
            },
          },
          $inc: { totalSubmissions: 1 },
        },
      );

      const bodyParse = JSON.parse(projectSnap.body);

      const mailData = {
        email: get(request, 'user.email', ''),
        to: get(request, 'user.email', ''),
        id: get(request, 'user._id', ''),
        subject: `We’ve Received Your SMASH Call Out Submission`,
        message_data: {
          viewCallout: `${process.env.WEBAPP_BASE_URL}/callouts`,
          calloutName: get(result, 'name', ''),
          snapshotImage: get(bodyParse, 'cover.coverPic', ''),
          title: get(bodyParse, 'cover.title', ''),
          username: get(bodyParse, 'creator.username', ''),
          logLine: get(bodyParse, 'basicInfo.logLine', ''),
          companyName: get(result, 'body.companyName', ''),
        },
      };
      await MailService.submissionSubmit(mailData);
      return h
        .response({
          statusCode: 200,
          message: 'Callout submission added successfully',
          data: result,
        })
        .code(200);
    } catch (err) {
      request.logger.error('error in CalloutController.addSubmission', err);
      errorHandler(err);
    }
  }

  /**
   * This method is responsible to add submission to the callout
   * @param {Hapi request obj} request
   * @param {hapi handler} h
   */
  static async addToSlate(request, h) {
    try {
      const { id } = request.params;
      const user = request.user;
      const payload = request.payload;
      // Perform the update
      const callout = await Callout.findOne({
        _id: id,
        'submissions._id': payload.id,
      });
      if (!callout) {
        request.logger.error(`${payload.id} callout does not exist!`);
        return h
          .response({
            statusCode: 404,
            message: `snapshot does not exist!`,
            error: `not_found`,
          })
          .code(404);
      }

      const submission = callout.submissions.find(
        (sub) => sub._id.toString() === payload.id,
      );

      const result = await CalloutService.patch(
        { _id: id },
        {
          $push: {
            slates: {
              creator: get(submission, 'creator'),
              creator: getCreatorFormateObject(user),

              'snapshot.id': get(submission, 'snapshot.id'),
              'snapshot.body': get(submission, 'snapshot.body'),
              'snapshot.title': get(submission, 'snapshot.title'),
              'snapshot.hash': get(submission, 'snapshot.hash'),
              submissionAddedAt: get(submission, 'addedAt'),
              notes: payload.notes,
              feedback: [],
            },
          },
          $inc: { totalSlates: 1, totalSubmissions: -1 },
        },
      );
      await CalloutService.patch(
        { _id: id },
        { $pull: { submissions: { _id: payload.id } } },
      );
      return h
        .response({
          statusCode: 200,
          message: 'Callout slate added successfully',
          data: result,
        })
        .code(200);
    } catch (err) {
      request.logger.error('error in CalloutController.addToSlate', err);
      errorHandler(err);
    }
  }

  /**
   * This method is update slate status inside the callout
   * @param {Hapi request obj} request
   * @param {hapi handler} h
   */
  static async updateSlateStatus(request, h) {
    try {
      const { id } = request.params;
      const payload = request.payload;
      const user = request.user;

      // Perform the update
      const callout = await Callout.findOne({
        _id: id,
        'slates._id': payload.id,
      });
      if (!callout) {
        request.logger.error(`${payload.id} callout does not exist!`);
        return h
          .response({
            statusCode: 404,
            message: `slates does not exist!`,
            error: `not_found`,
          })
          .code(404);
      }

      // Find the slate to get creator information
      const foundSlate = find(
        callout.slates,
        (slate) => String(slate._id) === String(payload.id),
      );

      if (foundSlate) {
        // Handle "not_interested" status with specific email to creator
        if (payload.status === 'not_interested') {
          const body = JSON.parse(get(foundSlate, 'snapshot.body', '{}'));
          const snapshotCreatorEmail = get(body, 'creator.email', '');
          const creatorName = get(body, 'creator.username', '');
          const mailData = {
            email: snapshotCreatorEmail,
            to: snapshotCreatorEmail,
            id: get(foundSlate, '_id', ''),
            subject: 'Update on your SMASH Call-out submission',
            agenda_type: 'not_interested_slate_email',
            message_data: {
              creatorName,
              calloutName: callout.name,
              calloutUrl: `${process.env.WEBAPP_BASE_URL}/callouts`,
            },
          };

          await MailService.notInterestedSlateEmail(mailData);
        }

        // Send slate status change email
        const isDiscoverer =
          String(get(callout, 'discoverer.id', '')) ===
          String(get(user, '_id', ''));

        if (isDiscoverer) {
          const body = get(foundSlate, 'snapshot.body', '{}');
          const project = JSON.parse(body);
          const calloutId = get(callout, '_id');

          const statusChangeMailData = {
            to: `${process.env.CUPID_EMAILS}`,
            id: get(foundSlate, '_id', ''),
            subject: 'Status Update on SMASH Call-out submission',
            agenda_type: 'slate_status_change_email',
            message_data: {
              viewCallout: `${process.env.WEBAPP_BASE_URL}/callouts/${id}`,
              calloutName: callout.name,
              slateStatus: getSlateStatusLabel(payload.status),
              feedback: payload.feedback || '',
              link: `${process.env.OPS_BASE_URL}/#/callouts/${calloutId}/show`,
              viewSnapshot: `${process.env.WEBAPP_BASE_URL}/project/snap/${get(
                foundSlate,
                'snapshot.hash',
                '',
              )}`,
              snapshotImage: get(project, 'cover.coverPic', ''),
              title: get(project, 'cover.title', ''),
              username: get(project, 'creator.username', ''),
              logLine: get(project, 'basicInfo.logLine', ''),
            },
          };

          await MailService.slateStatusChangeEmail(statusChangeMailData);
        }
      }

      const result = await CalloutService.patch(
        { _id: id, 'slates._id': payload.id },
        { $set: { 'slates.$.status': payload.status } },
      );

      return h
        .response({
          statusCode: 200,
          message: 'Callout slate status updated successfully',
          data: result,
        })
        .code(200);
    } catch (err) {
      request.logger.error('error in CalloutController.updateSlateStatus', err);
      errorHandler(err);
    }
  }

  /**
   * This method is update Submission status inside the callout
   * @param {Hapi request obj} request
   * @param {hapi handler} h
   */
  static async updateSubmissionStatus(request, h) {
    try {
      const { id } = request.params;
      const payload = request.payload;
      // Perform the update
      const callout = await Callout.findOne({
        _id: id,
        'submissions._id': payload.id,
      });
      if (!callout) {
        request.logger.error(`${payload.id} callout does not exist!`);
        return h
          .response({
            statusCode: 404,
            message: `submission does not exist!`,
            error: `not_found`,
          })
          .code(404);
      }

      const result = await CalloutService.patch(
        { _id: id, 'submissions._id': payload.id },
        { $set: { 'submissions.$.status': payload.status } },
      );
      if (payload.status === 'REJECTED') {
        const foundSubmission = find(
          result.submissions,
          (submission) => String(submission._id) === String(payload.id),
        );
        const mailData = {
          email: get(foundSubmission, 'creator.email', ''),
          to: get(foundSubmission, 'creator.email', ''),
          id: get(foundSubmission, '_id', ''),
          message_data: {
            creatorName: get(foundSubmission, 'creator.username', ''),
            calloutName: result.name,
            calloutUrl: `${process.env.WEBAPP_BASE_URL}/callouts`,
          },
        };
        await MailService.rejectSubmissionByCupid(mailData);
      }
      return h
        .response({
          statusCode: 200,
          message: 'Callout submission status updated successfully',
          data: result,
        })
        .code(200);
    } catch (err) {
      request.logger.error(
        'error in CalloutController.updateSubmissionStatus',
        err,
      );
      errorHandler(err);
    }
  }

  /**
   * This method is responsible to usend email to the discoverer of submissions in slate with email send false
   * @param {Hapi request obj} request
   * @param {hapi handler} h
   */
  static async sendEmailToDiscoverer(request, h) {
    try {
      const { id } = request.params;
      // Perform the update
      const callout = await Callout.findById(id);
      if (!callout) {
        request.logger.error(`${id} callout does not exist!`);
        return h
          .response({
            statusCode: 404,
            message: `callout does not exist!`,
            error: `not_found`,
          })
          .code(404);
      }

      const discovererEmail = get(callout, 'discoverer.email', '');
      const discovererName = get(callout, 'discoverer.name', '');

      const slatesEmailData = callout.slates
        .filter((slate) => !slate.isEmailSent)
        .map((slate) => {
          const body = get(slate, 'snapshot.body', '{}');
          const project = JSON.parse(body);
          return {
            coverPic: get(project, 'cover.coverPic', ''),
            title: get(project, 'cover.title', ''),
            username: get(
              project,
              'creator.username',
              get(project, 'creator.email', ''),
            ),
            logLine: get(project, 'basicInfo.logLine', ''),
            notes: get(slate, 'notes', ''),
            link: `${process.env.WEBAPP_BASE_URL}/project/snap/${get(
              slate,
              'snapshot.hash',
            )}?notes=${encodeURIComponent(
              get(slate, 'notes', ''),
            )}&id=${encodeURIComponent(id)}&slateId=${encodeURIComponent(
              get(slate, '_id', ''),
            )}`,
          };
        });

      if (slatesEmailData.length <= 0) {
        return h
          .response({
            statusCode: 400,
            message: 'No slates found with isEmailSent set to false.',
          })
          .code(400);
      }

      const mailData = {
        to: discovererEmail,
        id: get(callout, 'discoverer.id', ''),
        subject: `New submissions to your SMASH Call-out slate`,
        agenda_type: `submission_callout_slate_email`,
        message_data: {
          link: `${process.env.WEBAPP_BASE_URL}/callouts/${id}/slates`,
          email: discovererEmail,
          discovererName,
          projects: slatesEmailData,
        },
      };

      // Email to discoverer
      const result = await MailService.slateEmail(mailData);

      // Update isEmailSent to true for the filtered slates
      if (result) {
        await Callout.updateOne(
          { _id: id },
          { $set: { 'slates.$[elem].isEmailSent': true } },
          { arrayFilters: [{ 'elem.isEmailSent': false }] },
        );
      }

      return h
        .response({
          statusCode: 200,
          message: 'Callout submissions of slate email sent successfully',
          data: result,
        })
        .code(200);
    } catch (err) {
      request.logger.error(
        'error in CalloutController.sendEmailToDiscoverer',
        err,
      );
      errorHandler(err);
    }
  }

  /**
   * This method is responsible to delete submission from the callout
   * @param {Hapi request obj} request
   * @param {hapi handler} h
   */
  static async deleteSubmission(request, h) {
    try {
      const { id } = request.params;
      const payload = request.payload;
      // Perform the update
      const callout = await Callout.findOne({
        _id: id,
        'submissions._id': payload.id,
      });
      if (!callout) {
        request.logger.error(`${payload.id} callout does not exist!`);
        return h
          .response({
            statusCode: 404,
            message: `snapshot does not exist!`,
            error: `not_found`,
          })
          .code(404);
      }

      const result = await CalloutService.patch(
        { _id: id },
        {
          $pull: { submissions: { _id: payload.id } },
          $inc: { totalSubmissions: -1 },
        },
      );
      return h
        .response({
          statusCode: 200,
          message: 'Callout submission deleted successfully',
          data: result,
        })
        .code(200);
    } catch (err) {
      request.logger.error('error in CalloutController.deleteSubmission', err);
      errorHandler(err);
    }
  }

  /**
   * This method is responsible to delete slate from the callout
   * @param {Hapi request obj} request
   * @param {hapi handler} h
   */
  static async deleteSlate(request, h) {
    try {
      const { id } = request.params;
      const payload = request.payload;
      // Perform the update
      const callout = await Callout.findOne({
        _id: id,
        'slates._id': payload.id,
      });
      if (!callout) {
        request.logger.error(`${payload.id} callout does not exist!`);
        return h
          .response({
            statusCode: 404,
            message: `slates does not exist!`,
            error: `not_found`,
          })
          .code(404);
      }

      const slate = callout.slates.find(
        (sub) => sub._id.toString() === payload.id,
      );

      const result = await CalloutService.patch(
        { _id: id },
        {
          $push: {
            submissions: {
              'creator.userId': get(slate, 'creator.userId'),
              'creator.username': get(slate, 'creator.username'),
              'creator.avatar': get(slate, 'creator.avatar'),
              'creator.email': get(slate, 'creator.email'),
              'snapshot.id': get(slate, 'snapshot.id'),
              'snapshot.body': get(slate, 'snapshot.body'),
              'snapshot.title': get(slate, 'snapshot.title'),
              'snapshot.hash': get(slate, 'snapshot.hash'),
              addedAt: get(slate, 'submissionAddedAt'),
              isEmailSent: get(slate, 'isEmailSent'),
              feedback: [],
            },
          },
          $inc: { totalSlates: -1, totalSubmissions: 1 },
        },
      );
      await CalloutService.patch(
        { _id: id },
        { $pull: { slates: { _id: payload.id } } },
      );
      return h
        .response({
          statusCode: 200,
          message: 'Callout slate deleted successfully',
          data: result,
        })
        .code(200);
    } catch (err) {
      request.logger.error('error in CalloutController.deleteSlate', err);
      errorHandler(err);
    }
  }

  /**
   * This method is responsible to update feedback of slate inside the callout
   * @param {Hapi request obj} request
   * @param {hapi handler} h
   */
  static async addNotesToSlate(request, h) {
    try {
      const { id } = request.params;
      const payload = request.payload;
      // Perform the update
      const callout = await Callout.findOne({
        _id: id,
        'slates._id': payload.id,
      });
      if (!callout) {
        request.logger.error(`${payload.id} callout does not exist!`);
        return h
          .response({
            statusCode: 404,
            message: `slate does not exist!`,
            error: `not_found`,
          })
          .code(404);
      }

      const result = await CalloutService.patch(
        { _id: id, 'slates._id': payload.id },
        { $set: { 'slates.$.notes': payload.notes } },
      );

      return h
        .response({
          statusCode: 200,
          message: 'Callout slate notes updated successfully',
          data: result,
        })
        .code(200);
    } catch (err) {
      request.logger.error('error in CalloutController.addNotesToSlate', err);
      errorHandler(err);
    }
  }

  /**
   * This method is responsible to update feedback of submission inside the callout
   * @param {Hapi request obj} request
   * @param {hapi handler} h
   */
  static async addFeedbackToSubmission(request, h) {
    try {
      const { id } = request.params;
      const payload = request.payload;
      // Perform the update
      const callout = await Callout.findOne({
        _id: id,
        'submissions._id': payload.id,
      });
      if (!callout) {
        request.logger.error(`${payload.id} callout does not exist!`);
        return h
          .response({
            statusCode: 404,
            message: `submissions does not exist!`,
            error: `not_found`,
          })
          .code(404);
      }

      // First ensure feedback field is an array (in case it's null from old data)
      await CalloutService.patch(
        {
          _id: id,
          'submissions._id': payload.id,
          'submissions.$.feedback': null,
        },
        {
          $set: {
            'submissions.$.feedback': [],
          },
        },
      );

      const result = await CalloutService.patch(
        { _id: id, 'submissions._id': payload.id },
        {
          $push: {
            'submissions.$.feedback': {
              feedback: payload.feedback,
              addedAt: new Date(),
            },
          },
          $set: {
            'submissions.$.status': 'FEEDBACK_SENT',
          },
        },
      );

      const submission = callout.submissions.find(
        (sub) => sub._id.toString() === payload.id,
      );

      const body = get(submission, 'snapshot.body', '{}');
      const project = JSON.parse(body);

      const projectCreator = get(project, 'creator.email', '');

      const mailData = {
        email: projectCreator,
        to: projectCreator,
        id: get(submission, 'creator.userId', ''),
        subject: `Feedback on your submission`,
        agenda_type: `Feedback on your submission`,
        message_data: {
          link: `${process.env.WEBAPP_BASE_URL}/`,
          email: projectCreator,
          calloutName: callout.name,
          feedback: payload.feedback,
          viewCallout: `${process.env.WEBAPP_BASE_URL}/callouts/${id}`,
          viewSnapshot: `${process.env.WEBAPP_BASE_URL}/project/snap/${get(
            submission,
            'snapshot.hash',
            '',
          )}`,
          snapshotImage: get(project, 'cover.coverPic', ''),
          title: get(project, 'cover.title', ''),
          username: get(project, 'creator.username', ''),
          logLine: get(project, 'basicInfo.logLine', ''),
        },
      };

      await MailService.submissionFeedbackEmail(mailData);

      return h
        .response({
          statusCode: 200,
          message: 'Callout submissions feedback updated successfully',
          data: result,
        })
        .code(200);
    } catch (err) {
      request.logger.error(
        'error in CalloutController.addFeedbackToSubmission',
        err,
      );
      errorHandler(err);
    }
  }

  /**
   * This method is responsible to update feedback of submission inside the callout
   * @param {Hapi request obj} request
   * @param {hapi handler} h
   */
  static async addFeedbackToSlate(request, h) {
    try {
      const { id } = request.params;
      const payload = request.payload;
      // Perform the update
      const callout = await Callout.findOne({
        _id: id,
        'slates._id': payload.id,
      });
      if (!callout) {
        request.logger.error(`${payload.id} callout does not exist!`);
        return h
          .response({
            statusCode: 404,
            message: `slates does not exist!`,
            error: `not_found`,
          })
          .code(404);
      }

      // First ensure feedback field is an array (in case it's null from old data)
      await CalloutService.patch(
        { _id: id, 'slates._id': payload.id, 'slates.$.feedback': null },
        {
          $set: {
            'slates.$.feedback': [],
          },
        },
      );

      const result = await CalloutService.patch(
        { _id: id, 'slates._id': payload.id },
        {
          $push: {
            'slates.$.feedback': {
              feedback: payload.feedback,
              addedAt: new Date(),
            },
          },
        },
      );

      const slate = callout.slates.find(
        (sub) => sub._id.toString() === payload.id,
      );

      const body = get(slate, 'snapshot.body', '{}');
      const project = JSON.parse(body);

      const projectCreator = get(project, 'creator.email', '');
      const mailData = {
        email: projectCreator,
        to: projectCreator,
        id: get(slate, 'creator.userId', ''),
        subject: `Feedback on your submission`,
        agenda_type: `Feedback on your submission`,
        message_data: {
          link: `${process.env.WEBAPP_BASE_URL}/`,
          email: projectCreator,
          calloutName: callout.name,
          feedback: payload.feedback,
          viewCallout: `${process.env.WEBAPP_BASE_URL}/callouts/${id}`,
          viewSnapshot: `${process.env.WEBAPP_BASE_URL}/project/snap/${get(
            slate,
            'snapshot.hash',
            '',
          )}`,
          snapshotImage: get(project, 'cover.coverPic', ''),
          title: get(project, 'cover.title', ''),
          username: get(project, 'creator.username', ''),
          logLine: get(project, 'basicInfo.logLine', ''),
        },
      };

      await MailService.submissionFeedbackEmail(mailData);

      return h
        .response({
          statusCode: 200,
          message: 'Callout slate feedback updated successfully',
          data: result,
        })
        .code(200);
    } catch (err) {
      request.logger.error(
        'error in CalloutController.addFeedbackToSlate',
        err,
      );
      errorHandler(err);
    }
  }

  /**
   * This method is responsible for delete tag
   * Get key and value from params
   * @param {Hapi request obj} request
   * @param {hapi handler} h
   */

  static async delete(request, h) {
    try {
      const { id } = request.payload;
      /* Remove Callout */
      const result = await CalloutService.remove(
        { _id: { $in: id } },
        { multi: true },
      );
      request.logger.info(`Callout deleted successfully.`);
      return h
        .response({
          statusCode: 200,
          message: 'Callout deleted successfully',
          data: result,
        })
        .code(200);
    } catch (err) {
      request.logger.error('error in CalloutController.delete', err);
      errorHandler(err);
    }
  }

  /**
   * This method is responsible for delete tag
   * Get key and value from params
   * @param {Hapi request obj} request
   * @param {hapi handler} h
   */
  static async softDelete(request, h) {
    try {
      const { ids } = request.payload;

      /* Remove organizations */
      const result = await CalloutService.patch(
        { _id: { $in: ids } },
        { deleted: true },
        { multi: true },
      );
      request.logger.info(`Callout soft deleted successfully.`);
      return h
        .response({
          statusCode: 200,
          message: 'Callout soft deleted successfully',
          data: result,
        })
        .code(200);
    } catch (err) {
      request.logger.error('error in CalloutController.softdelete', err);
      errorHandler(err);
    }
  }

  /**
   * This method is responsible for get callout by id
   * Get id from params
   * @param {Hapi request obj} request
   * @param {hapi handler} h
   */
  static async getPublicCallout(request, h) {
    try {
      const { id } = request.params;
      const { options } = request.parsedQuery;
      const result = await CalloutService.getById(id, options);
      return h
        .response({
          statusCode: 200,
          message: 'Get Callout successfully',
          data: result,
        })
        .code(200);
    } catch (err) {
      request.logger.error('error in CalloutController.getPublicCallout', err);
      errorHandler(err);
    }
  }

  /**
   * This method is responsible to add slate to the callout
   * @param {Hapi request obj} request
   * @param {hapi handler} h
   */
  static async addSnapshotToSlate(request, h) {
    try {
      const { id } = request.params;
      const { user, payload } = request;

      const callout = await Callout.findOne({ _id: id });
      if (!callout) {
        request.logger.error(`${id} callout does not exist!`);
        return h
          .response({
            statusCode: 404,
            message: `Call-out does not exist!`,
            error: `not_found`,
          })
          .code(404);
      }

      const isSnapshotExist = get(callout, 'slates', []).some((slate) => {
        return String(slate.snapshot.id) === String(payload.id);
      });

      if (isSnapshotExist) {
        return h
          .response({
            statusCode: 400,
            message: `Snapshot already exist in slate!`,
            error: `bad_request`,
          })
          .code(400);
      }

      const snapshot = await ProjectSnaps.findOne({ _id: payload.id });
      if (!snapshot) {
        request.logger.error(`${payload.id} snapshot does not exist!`);
        return h
          .response({
            statusCode: 404,
            message: `Snapshot does not exist!`,
            error: `not_found`,
          })
          .code(404);
      }

      const result = await CalloutService.patch(
        { _id: id },
        {
          $push: {
            slates: {
              creator: getCreatorFormateObject(user),
              'snapshot.id': get(snapshot, 'id'),
              'snapshot.body': get(snapshot, 'body'),
              'snapshot.title': get(snapshot, 'notes'),
              'snapshot.hash': get(snapshot, 'hash'),
              submissionAddedAt: new Date(),
              notes: payload.notes,
              feedback: [],
            },
          },
          $inc: { totalSlates: 1 },
        },
      );

      return h
        .response({
          statusCode: 200,
          message: 'Callout slate added successfully',
          data: result,
        })
        .code(200);
    } catch (err) {
      request.logger.error(err, 'Error in CalloutController.addToSlate');
      errorHandler(err);
    }
  }

  /**
   * This method is responsible to add submission to the callout
   *
   * @param {Hapi request obj} request
   * @param {hapi handler} h
   */
  static async addSnapshotToSubmission(request, h) {
    try {
      const { id } = request.params;
      const { user, payload } = request;

      const callout = await Callout.findOne({ _id: id });
      if (!callout) {
        request.logger.error(`${id} callout does not exist!`);
        return h
          .response({
            statusCode: 404,
            message: `Call-out does not exist!`,
            error: `not_found`,
          })
          .code(404);
      }

      const isSnapshotExist = get(callout, 'submissions', []).some(
        (submission) => {
          return String(submission.snapshot.id) === String(payload.id);
        },
      );

      if (isSnapshotExist) {
        return h
          .response({
            statusCode: 400,
            message: `Snapshot already exist in submission!`,
            error: `bad_request`,
          })
          .code(400);
      }

      const snapshot = await ProjectSnaps.findOne({ _id: payload.id });
      if (!snapshot) {
        request.logger.error(`${payload.id} snapshot does not exist!`);
        return h
          .response({
            statusCode: 404,
            message: `Snapshot does not exist!`,
            error: `not_found`,
          })
          .code(404);
      }
      const auth0Id = get(request, 'user.auth0Id');
      const token = await AuthService.authM2MToken();
      const roles = await AuthService.Auth0UserRoles(auth0Id, token);
      const isAdmin = roles.some((role) => role.name === 'admin');
      const creator = getCreatorFormateObject(user);
      if (isAdmin) {
        creator.submittedBy = 'cupid';
      } else {
        creator.submittedBy = 'user';
      }
      const result = await CalloutService.patch(
        { _id: id },
        {
          $push: {
            submissions: {
              creator,
              'snapshot.id': get(snapshot, 'id'),
              'snapshot.body': get(snapshot, 'body'),
              'snapshot.title': get(snapshot, 'notes'),
              'snapshot.hash': get(snapshot, 'hash'),
              addedAt: new Date(),
              isEmailSent: false,
              status: 'NEW',
              feedback: [],
            },
          },
          $inc: { totalSubmissions: 1 },
        },
      );

      return h
        .response({
          statusCode: 200,
          message: 'Snapshot added successfully',
          data: result,
        })
        .code(200);
    } catch (err) {
      request.logger.error(
        err,
        'Error in CalloutController.addSnapshotToSubmission',
      );
      errorHandler(err);
    }
  }

  /**
   * This method is responsible to send mail to discoverer
   *
   * @param {Hapi request obj} request
   * @param {hapi handler} h
   */
  static async sendDiscovererEmail(request, h) {
    try {
      const { payload } = request;
      const result = await CalloutService.getById(payload.id);
      const mailData = {
        email: get(result, 'discoverer.email', ''),
        to: get(result, 'discoverer.email', ''),
        id: get(result, 'discoverer.id', ''),
        subject: `Your SMASH Call Out draft is ready to be reviewed`,
        agenda_type: `Your SMASH Call Out draft is ready to be reviewed`,
        message_data: {
          link: `${process.env.WEBAPP_BASE_URL}/callouts/view/${payload.id}`,
          buttonLink: payload.buttonLink,
          discoverername: get(result, 'discoverer.name', ''),
          email: get(result, 'discoverer.email', ''),
          calloutLogo: get(result, 'body.logo', ''),
          companyName: get(result, 'body.companyName', ''),
          name: get(result, 'name', ''),
        },
      };

      await MailService.calloutReadyToReviewEmail(mailData);
      return h
        .response({
          statusCode: 200,
          message: 'Get Callout successfully',
          data: result,
        })
        .code(200);
    } catch (err) {
      request.logger.error('error in CalloutController.getPublicCallout', err);
      errorHandler(err);
    }
  }

  /**
   * This method is responsible to get the callout details with subscription status
   * @param {Hapi request obj} request
   * @param {hapi handler} h
   */
  static async getCalloutDetails(request, h) {
    try {
      const { id } = request.params;
      const result = await CalloutService.getById(id);
      const discovererId = get(result, 'discoverer.id', '');
      const subscriptionData = await subscriptionServices.find({
        'user.userId': discovererId,
      });
      const subscription = last(subscriptionData);
      const discovererEmail = get(result, 'discoverer.email');
      const userDetails = await userSerVice.getUserList(
        { email: discovererEmail },
        discovererEmail,
      );
      if (!request.payload) {
        request.payload = {};
      }
      request.payload.slug = 'enterprise';
      const planData = await PlanController.getPlanBySlug(request);
      const subscriptionResult = {
        ...result,
        userMeta: {
          type: get(userDetails, 'docs[0].userMeta.type', 'free'),
          status: get(userDetails, 'docs[0].userMeta.status', 'draft'),
        },
        enterPrisePlanId: get(planData, '_id'),
        enterPrisePriceId: get(planData, 'productPriceId'),
        subscriptionStatus: subscription ? subscription.status : false,
      };

      return h
        .response({
          statusCode: 200,
          message: 'Get Callout successfully',
          data: subscriptionResult,
        })
        .code(200);
    } catch (err) {
      request.logger.error('error in CalloutController.getPublicCallout', err);
      errorHandler(err);
    }
  }

  /**
   * Get Callout by query params
   * Parse query from query params
   * @param {Hapi request obj} request
   * @param {hapi handler} h
   */
  static async list(request, h) {
    try {
      const query = request.parsedQuery;
      const { where, options } = query;

      const aggregate = [
        { $match: where },
        {
          $lookup: {
            from: 'subscriptions',
            localField: 'discoverer.id',
            foreignField: 'user.userId',
            as: 'subscriptions',
          },
        },
        {
          $addFields: {
            hasActiveSubscription: {
              $in: ['active', '$subscriptions.status'],
            },
            newsubmissionsCount: {
              $size: {
                $filter: {
                  input: '$submissions',
                  as: 'submission',
                  cond: { $eq: ['$$submission.status', 'NEW'] },
                },
              },
            },
          },
        },
      ];

      if (options.sort) {
        aggregate.push({ $sort: options.sort });
      }

      aggregate.push({
        $facet: {
          metadata: [{ $count: 'totalMatchingRecords' }],
          data: [
            { $skip: options.offset },
            { $limit: options.limit },
            {
              $group: {
                _id: null,
                count: { $sum: 1 },
                results: { $push: '$$ROOT' },
              },
            },
            {
              $project: {
                count: 1,
                rows: '$results',
              },
            },
          ],
        },
      });

      /* Add totalMatchingRecords to the result */
      aggregate.push({
        $addFields: {
          totalMatchingRecords: {
            $arrayElemAt: ['$metadata.totalMatchingRecords', 0],
          },
        },
      });

      /* Fetch callout list using aggregation */
      const result = await CalloutService.aggregation(aggregate);
      const newArray = result.data[0].rows
        .map((item) => String(get(item, 'discoverer.id', null)))
        .filter((id) => id !== 'null');
      const uniqueIds = uniq(newArray);
      const token = get(request, 'headers.authorization');
      const userDetails = await userSerVice.getUserOpsList(
        `?_id[$in]=${uniqueIds}&$limit=100`,
        token,
      );
      const mergedData = result.data[0].rows.map((prevItem) => {
        const email = get(prevItem, 'discoverer.email', null);
        const matchingNewData = find(
          userDetails.docs,
          (item) => item.email === email,
        );
        if (matchingNewData) {
          return {
            ...prevItem,
            discoverer: {
              ...prevItem.discoverer,
            },
            userMeta: {
              type: get(matchingNewData, 'userMeta.type', 'free'),
              status: get(matchingNewData, 'userMeta.status', 'draft'),
            },
          };
        } else {
          return {
            ...prevItem,
            discoverer: {
              ...prevItem.discoverer,
            },
            userMeta: {
              type: null,
              status: null,
            },
          };
        }
      });

      return h
        .response({
          statusCode: 200,
          message: 'Get Callout successfully',
          data: {
            docs: mergedData,
            total: result.totalMatchingRecords || 0,
            limit: options.limit,
            offset: options.offset,
          },
        })
        .code(200);
    } catch (err) {
      request.logger.error('error in CalloutController.get', err);
      errorHandler(err);
    }
  }

  /**
   * This method is responsible to get the callout submissions and slates and add also filter for submission
   * @param {Hapi request obj} request
   * @param {hapi handler} h
   */
  static async getSubmissionsAndSlateById(request, h) {
    try {
      const query = request.parsedQuery;
      const { where, options } = query;
      let { offset, limit, select, sort } = options;
      const skip = offset;

      if (isEmpty(select)) {
        select = '_id';
      }
      if (where['submissions.status']) {
        where['submissions.status'] = toUpper(where['submissions.status']);
      }
      const projectFields = select.split(' ').reduce((acc, field) => {
        acc[field] = 1;
        return acc;
      }, {});
      const shouldFetchAll =
        get(sort, 'projectName', null) || get(sort, 'genre', null);

      // Main pipeline
      let result;
      let pipeline = [];
      let totalCountPipeline = [];
      if (where.action === 'both') {
        const projectField = select.split(' ').reduce((acc, field) => {
          acc[field] = `$${field}`;
          return acc;
        }, {});
        const userId = where.userId;
        delete where.userId;
        pipeline = [
          {
            $match: {
              $or: [
                { 'submissions.creator.userId': userId },
                { 'slates.creator.userId': userId },
              ],
            },
          },
          {
            $project: {
              ...projectField,
              mergedData: {
                $concatArrays: [
                  {
                    $map: {
                      input: {
                        $filter: {
                          input: '$submissions',
                          as: 'sub',
                          cond: { $eq: ['$$sub.creator.userId', userId] },
                        },
                      },
                      as: 'filteredSub',
                      in: {
                        ...projectField,
                        submissions: '$$filteredSub',
                        type: 'submission',
                      },
                    },
                  },
                  {
                    $map: {
                      input: {
                        $filter: {
                          input: '$slates',
                          as: 'slate',
                          cond: { $eq: ['$$slate.creator.userId', userId] },
                        },
                      },
                      as: 'filteredSlate',
                      in: {
                        ...projectField,
                        submissions: '$$filteredSlate',
                        type: 'slate',
                      },
                    },
                  },
                ],
              },
            },
          },
          { $unwind: '$mergedData' },
          { $replaceRoot: { newRoot: '$mergedData' } },
        ];

        totalCountPipeline = [
          {
            $match: {
              $or: [
                { 'submissions.creator.userId': userId },
                { 'slates.creator.userId': userId },
              ],
            },
          },
          {
            $project: {
              mergedData: {
                $concatArrays: [
                  {
                    $filter: {
                      input: '$submissions',
                      as: 'sub',
                      cond: {
                        $eq: ['$$sub.creator.userId', userId],
                      },
                    },
                  },
                  {
                    $filter: {
                      input: '$slates',
                      as: 'slate',
                      cond: {
                        $eq: ['$$slate.creator.userId', userId],
                      },
                    },
                  },
                ],
              },
            },
          },
          { $unwind: '$mergedData' },
          { $count: 'total' },
        ];
      } else {
        const selectedAction =
          where.action === 'slates' ? 'slates' : 'submissions';
        delete where.action;
        pipeline = [
          { $unwind: `$${selectedAction}` },
          { $match: where },
          { $project: projectFields },
        ];
        totalCountPipeline = [
          { $unwind: `$${selectedAction}` },
          { $match: where },
          { $count: 'total' },
        ];
      }
      if (!shouldFetchAll) {
        pipeline.push({ $sort: sort });
        pipeline.push({ $skip: skip });
        pipeline.push({ $limit: parseInt(limit) });
      }

      result = await Callout.aggregate(pipeline);
      if (shouldFetchAll && result.length > 0) {
        let sortField = null;
        let sortOrder = 1;
        if (sort.projectName !== undefined) {
          sortField = 'cover.title';
          sortOrder = sort.projectName === 1 ? 1 : -1;
        } else if (sort.genre !== undefined) {
          sortField = 'basicInfo.genre';
          sortOrder = sort.genre === 1 ? 1 : -1;
        }

        if (sortField) {
          result.sort((a, b) => {
            try {
              const bodyA = JSON.parse(a.submissions.snapshot.body);
              const bodyB = JSON.parse(b.submissions.snapshot.body);

              let valueA = '';
              let valueB = '';

              // Extract the correct sorting field
              if (sortField === 'cover.title') {
                valueA = bodyA.cover.title || '';
                valueB = bodyB.cover.title || '';
              } else if (sortField === 'basicInfo.genre') {
                valueA = bodyA.basicInfo.genre || '';
                valueB = bodyB.basicInfo.genre || '';
              }

              return valueA.localeCompare(valueB) * sortOrder;
            } catch (error) {
              console.error('Error parsing body:', error);
              return 0; // Keep items in the same order if JSON parsing fails
            }
          });
        }

        if (shouldFetchAll) {
          result = result.slice(offset, offset + parseInt(limit));
        }
      }
      const totalCount = await Callout.aggregate(totalCountPipeline);

      return h
        .response({
          statusCode: 200,
          message: 'Data retrieved successfully',
          data: {
            docs: result,
            total: get(totalCount, '[0].total', 0),
            limit,
            offset: skip,
            page: floor(offset / limit + 1),
            pages: Math.ceil(get(totalCount, '[0].total', 0) / limit),
          },
        })
        .code(200);
    } catch (err) {
      request.logger.error('error in CalloutController.getPublicCallout', err);
      errorHandler(err);
    }
  }

  /**
   * This method is responsible to get callout submissions filtered by project ID
   * @param {Hapi request obj} request
   * @param {hapi handler} h
   */
  static async getSubmissionsByProjectId(request, h) {
    try {
      const query = request.parsedQuery;
      const { where, options } = query;
      let { offset, limit, select, sort } = options;
      const skip = offset;
      const projectId = where.projectId;

      if (isEmpty(select)) {
        select = '_id';
      }
      if (where['submissions.status']) {
        where['submissions.status'] = toUpper(where['submissions.status']);
      }

      // Aggregation pipeline to get submissions for a specific project
      const aggregate = [
        {
          $match: {
            deleted: { $ne: true },
            $or: [
              { 'submissions.0': { $exists: true } },
              { 'slates.0': { $exists: true } },
            ],
          },
        },
        {
          $lookup: {
            from: 'projectsnaps',
            localField: 'submissions.snapshot.id',
            foreignField: '_id',
            as: 'submissionSnapshots',
          },
        },
        {
          $lookup: {
            from: 'projectsnaps',
            localField: 'slates.snapshot.id',
            foreignField: '_id',
            as: 'slateSnapshots',
          },
        },
        {
          $addFields: {
            filteredSubmissions: {
              $filter: {
                input: '$submissions',
                as: 'sub',
                cond: {
                  $in: [
                    '$$sub.snapshot.id',
                    {
                      $map: {
                        input: {
                          $filter: {
                            input: '$submissionSnapshots',
                            as: 'snap',
                            cond: {
                              $eq: [
                                '$$snap.projectId',
                                { $toObjectId: projectId },
                              ],
                            },
                          },
                        },
                        as: 'filteredSnap',
                        in: '$$filteredSnap._id',
                      },
                    },
                  ],
                },
              },
            },
            filteredSlates: {
              $filter: {
                input: '$slates',
                as: 'slate',
                cond: {
                  $in: [
                    '$$slate.snapshot.id',
                    {
                      $map: {
                        input: {
                          $filter: {
                            input: '$slateSnapshots',
                            as: 'snap',
                            cond: {
                              $eq: [
                                '$$snap.projectId',
                                { $toObjectId: projectId },
                              ],
                            },
                          },
                        },
                        as: 'filteredSnap',
                        in: '$$filteredSnap._id',
                      },
                    },
                  ],
                },
              },
            },
          },
        },
        {
          $project: {
            _id: 1,
            name: 1,
            mergedData: {
              $concatArrays: [
                {
                  $map: {
                    input: '$filteredSubmissions',
                    as: 'sub',
                    in: {
                      _id: '$$sub._id',
                      calloutId: '$_id',
                      calloutName: '$name',
                      snapshot: '$$sub.snapshot',
                      creator: '$$sub.creator',
                      addedAt: '$$sub.addedAt',
                      status: '$$sub.status',
                      feedback: '$$sub.feedback',
                      type: 'submission',
                    },
                  },
                },
                {
                  $map: {
                    input: '$filteredSlates',
                    as: 'slate',
                    in: {
                      _id: '$$slate._id',
                      calloutId: '$_id',
                      calloutName: '$name',
                      snapshot: '$$slate.snapshot',
                      creator: '$$slate.creator',
                      addedAt: '$$slate.addedAt',
                      status: '$$slate.status',
                      feedback: '$$slate.feedback',
                      notes: '$$slate.notes',
                      type: 'slate',
                    },
                  },
                },
              ],
            },
          },
        },
        { $unwind: '$mergedData' },
        { $replaceRoot: { newRoot: '$mergedData' } },
        { $sort: sort || { addedAt: -1 } },
        { $skip: skip },
        { $limit: limit },
      ];

      const result = await Callout.aggregate(aggregate);

      // Count total for pagination
      const countAggregate = [
        {
          $match: {
            deleted: { $ne: true },
            $or: [
              { 'submissions.0': { $exists: true } },
              { 'slates.0': { $exists: true } },
            ],
          },
        },
        {
          $lookup: {
            from: 'projectsnaps',
            localField: 'submissions.snapshot.id',
            foreignField: '_id',
            as: 'submissionSnapshots',
          },
        },
        {
          $lookup: {
            from: 'projectsnaps',
            localField: 'slates.snapshot.id',
            foreignField: '_id',
            as: 'slateSnapshots',
          },
        },
        {
          $addFields: {
            filteredSubmissions: {
              $filter: {
                input: '$submissions',
                as: 'sub',
                cond: {
                  $in: [
                    '$$sub.snapshot.id',
                    {
                      $map: {
                        input: {
                          $filter: {
                            input: '$submissionSnapshots',
                            as: 'snap',
                            cond: {
                              $eq: [
                                '$$snap.projectId',
                                { $toObjectId: projectId },
                              ],
                            },
                          },
                        },
                        as: 'filteredSnap',
                        in: '$$filteredSnap._id',
                      },
                    },
                  ],
                },
              },
            },
            filteredSlates: {
              $filter: {
                input: '$slates',
                as: 'slate',
                cond: {
                  $in: [
                    '$$slate.snapshot.id',
                    {
                      $map: {
                        input: {
                          $filter: {
                            input: '$slateSnapshots',
                            as: 'snap',
                            cond: {
                              $eq: [
                                '$$snap.projectId',
                                { $toObjectId: projectId },
                              ],
                            },
                          },
                        },
                        as: 'filteredSnap',
                        in: '$$filteredSnap._id',
                      },
                    },
                  ],
                },
              },
            },
          },
        },
        {
          $project: {
            totalCount: {
              $add: [
                { $size: '$filteredSubmissions' },
                { $size: '$filteredSlates' },
              ],
            },
          },
        },
        {
          $group: {
            _id: null,
            total: { $sum: '$totalCount' },
          },
        },
      ];

      const totalCount = await Callout.aggregate(countAggregate);

      return h
        .response({
          statusCode: 200,
          message: 'Data retrieved successfully',
          data: {
            docs: result,
            total: get(totalCount, '[0].total', 0),
            limit,
            offset: skip,
            page: Math.floor(offset / limit + 1),
            pages: Math.ceil(get(totalCount, '[0].total', 0) / limit),
          },
        })
        .code(200);
    } catch (err) {
      request.logger.error(
        'error in CalloutController.getSubmissionsByProjectId',
        err,
      );
      errorHandler(err);
    }
  }
}

module.exports = CalloutController;
