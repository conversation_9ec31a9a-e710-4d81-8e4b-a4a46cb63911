import React from 'react';
import { get } from 'lodash';
import { Link, useParams } from 'react-router-dom';
import {
  List,
  Datagrid,
  TextField,
  FunctionField,
  // useRedirect,
  TopToolbar,
} from 'react-admin';
import InboxIcon from '@mui/icons-material/Inbox';
import { Stack, Box, Typography } from '@mui/material';
import { getSubmissionStatusBadge } from '../../helpers/helper';
import Badge from '../../sharedComponents/badge';

const SubmissionList = (props) => {
  const { id } = useParams();
  // const redirect = useRedirect();

  const CustomToolbar = () => (
    <TopToolbar sx={{ minHeight: '10px !important' }} />
  );

  return (
    <Box sx={{ mt: 4, p: 2, border: '1px solid #ddd', borderRadius: '5px' }}>
      <Stack
        direction="row"
        spacing={2}
        sx={{ justifyContent: 'space-between' }}
      >
        <Typography variant="subtitle1" fontWeight="bold">
          Submissions
        </Typography>
      </Stack>
      <List
        resource="userSubmission"
        exporter={false}
        filter={{
          userId: id,
        }}
        disableSyncWithLocation
        title={false}
        empty={
          <>
            <Box textAlign="center" p={2}>
              <InboxIcon sx={{ fontSize: 100 }} />
              <Typography variant="h6" color="textSecondary">
                No Submissions Yet
              </Typography>
            </Box>
          </>
        }
        actions={<CustomToolbar />}
      >
        <Datagrid bulkActionButtons={false}>
          <FunctionField
            source="snapshotName"
            label={<span style={{ fontWeight: 'bold' }}>Snapshot name</span>}
            render={(record) => (
              <Link
                className="admin-link"
                target="_blank"
                to={`${process.env.REACT_APP_WEBAPP_URL}/project/snap/${record.hash}`}
              >
                {record.snapshotName}
              </Link>
            )}
          />

          <FunctionField
            source="snapProjectName"
            label={<span style={{ fontWeight: 'bold' }}>Project name</span>}
            render={(record) => (
              <Link
                className="admin-link"
                to={`/projects/${record.projectId}/show`}
              >
                {record.snapProjectName}
              </Link>
            )}
          />

          <FunctionField
            source="callOutName"
            label={<span style={{ fontWeight: 'bold' }}>Call out</span>}
            render={(record) => (
              <Link
                className="admin-link"
                to={`/callouts/${record.calloutId}/show`}
              >
                {record.callOutName}
              </Link>
            )}
          />

          <TextField
            source="genre"
            label={
              <span
                style={{
                  fontWeight: 'bold',
                  fontSize: '14px',
                  color: 'black',
                }}
              >
                Genre
              </span>
            }
          />

          <TextField
            source="snapshotCreateAt"
            label={
              <span
                style={{
                  fontWeight: 'bold',
                  fontSize: '14px',
                  color: 'black',
                }}
              >
                Date Submitted
              </span>
            }
          />
          <FunctionField
            source="submissionsStatus"
            label={<span style={{ fontWeight: 'bold' }}>Status</span>}
            render={(record) => {
              const subsStatus = get(record, 'submissionsStatus');
              const { badgeColor, labelText } =
                getSubmissionStatusBadge(subsStatus);

              const isCupid =
                get(record, 'submittedBy') === 'cupid' &&
                get(record, 'submissionsStatus') === 'NEW';

              const finalLabelText = isCupid ? 'Cupid Selected' : labelText;
              const finalBadgeColor = isCupid ? 'badge-success' : badgeColor;

              return (
                <Badge
                  list={[{ label: finalLabelText, className: finalBadgeColor }]}
                />
              );
            }}
          />
        </Datagrid>
      </List>
    </Box>
  );
};

export default SubmissionList;
